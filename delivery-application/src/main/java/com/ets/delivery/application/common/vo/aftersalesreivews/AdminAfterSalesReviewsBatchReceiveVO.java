package com.ets.delivery.application.common.vo.aftersalesreivews;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 售后审核批量领取返回数据
 */
@Data
public class AdminAfterSalesReviewsBatchReceiveVO {

    private Integer id;

    /**
     * 审核单号
     */
    private String reviewSn;

    /**
     * 业务单号
     */
    private String orderSn;

    /**
     * 业务类型
     */
    private String orderType;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    /**
     * 审核状态[0-待审核 1-审核通过 2-审核驳回 3-已取消]
     */
    private Integer reviewStatus;

    /**
     * 领取状态[0-未领取 1-已领取]
     */
    private Integer receiveStatus;

    /**
     * 领取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveTime;

    /**
     * 操作人
     */
    private String operator;
}
