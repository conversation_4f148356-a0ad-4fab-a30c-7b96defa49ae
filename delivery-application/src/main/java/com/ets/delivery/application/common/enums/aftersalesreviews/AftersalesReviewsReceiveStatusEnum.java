package com.ets.delivery.application.common.enums.aftersalesreviews;

import com.ets.delivery.application.common.vo.SelectOptionVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 售后审核领取状态枚举
 */
@Getter
@AllArgsConstructor
public enum AftersalesReviewsReceiveStatusEnum {

    NOT_RECEIVED(0, "未领取"),
    RECEIVED(1, "已领取");

    private final Integer value;
    private final String label;

    public static final Map<Integer, String> map = Arrays.stream(values())
            .collect(Collectors.toMap(AftersalesReviewsReceiveStatusEnum::getValue, AftersalesReviewsReceiveStatusEnum::getLabel));

    public static List<SelectOptionVO> getSelectOptions() {
        return Arrays.stream(values())
                .map(item -> new SelectOptionVO(item.getLabel(), item.getValue()))
                .collect(Collectors.toList());
    }
}
