package com.ets.delivery.application.app.business.aftersalesreviews;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.delivery.application.app.disposer.AfterSalesReviewAutoAuditDisposer;
import com.ets.delivery.application.app.thirdservice.feign.AfterSalesReviewsNotifyFeign;
import com.ets.delivery.application.common.bo.aftersalesreviews.AfterSalesReviewAutoAuditBO;
import com.ets.delivery.application.app.thirdservice.request.AfterSalesReviewsNotifyDTO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.config.queue.review.QueueReview;
import com.ets.delivery.application.common.consts.aftersalesreviews.*;
import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.enums.aftersalesreviews.AftersalesReviewsReceiveStatusEnum;
import com.ets.delivery.application.common.dto.aftersalesreviews.*;
import com.ets.delivery.application.common.vo.MultiSelectOptionVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsBatchReceiveVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsDetailVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsListVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsSelectOptionVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AfterSalesReviewsVO;
import com.ets.delivery.application.infra.entity.Setting;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviewsLog;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.delivery.application.infra.service.AftersalesReviewsLogService;
import com.ets.delivery.application.infra.service.AftersalesReviewsService;
import com.ets.delivery.application.infra.service.AftersalesReviewsVehiclesService;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.infra.service.SettingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 售后审核单业务逻辑
 */
@Slf4j
@Component
public class AfterSalesReviewsBusiness {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private QueueReview queue;

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    @Autowired
    private AftersalesReviewsLogService aftersalesReviewsLogService;

    @Autowired
    private SettingService settingService;

    @Autowired
    private AfterSalesReviewsNotifyFeign afterSalesReviewsNotifyFeign;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    /**
     * 创建售后审核单
     *
     * @param dto 创建售后审核单请求参数
     * @return 售后审核单
     */
    public AfterSalesReviewsVO createAfterSalesReview(CreateAfterSalesReviewDTO dto) {
        // 检查业务单是否存在待审核记录
        AftersalesReviews reviews = aftersalesReviewsService.getByOrderSn(dto.getOrderSn());
        if (reviews != null && reviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.PENDING.getValue())) {
            ToolsHelper.throwException("该业务单已存在待审核记录，请勿重复提交");
        }

        // 创建审核单
        AftersalesReviews aftersalesReviews = BeanUtil.copyProperties(dto, AftersalesReviews.class);

        // 生成审核单流水号
        String reviewSn = ToolsHelper.genNum(redisPermanentTemplate, "AfterSalesReviews", active, 8);

        // 保存审核行驶证信息
        if (dto.getReviewVehicleInfo() != null) {
            // 校验必传参数
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getFrontImgUrl())) {
                ToolsHelper.throwException("审核行驶证印章页照片不能为空");
            }
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getBackImgUrl())) {
                ToolsHelper.throwException("审核行驶证条码页照片不能为空");
            }
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getFrontCarImgUrl())) {
                ToolsHelper.throwException("审核行驶证车头照不能为空");
            }
            if (ObjectUtils.isEmpty(dto.getReviewVehicleInfo().getGearActivateImgUrl())) {
                ToolsHelper.throwException("审核行驶证车内照不能为空");
            }
            saveVehicleInfo(reviewSn, dto.getReviewVehicleInfo(), AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue());
        }

        // 保存订单行驶证信息
        if (dto.getOrderVehicleInfo() != null) {
            saveVehicleInfo(reviewSn, dto.getOrderVehicleInfo(), AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue());
        }

        // 设置属性
        aftersalesReviews.setReviewSn(reviewSn);
        aftersalesReviews.setApplyTime(LocalDateTime.now());
        aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.PENDING.getValue()); // 待审核
        aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.DEFAULT.getValue()); // 未通知

        // 保存审核单
        aftersalesReviewsService.create(aftersalesReviews);

        // 记录操作日志
        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
        reviewsLog.setReviewSn(reviewSn);
        reviewsLog.setOperateContent("创建售后审核");
        reviewsLog.setOperator("system");
        reviewsLog.setType("create");
        aftersalesReviewsLogService.create(reviewsLog);

        log.info("创建售后审核单成功，审核单号：{}", reviewSn);

        // 异步判断是否需要人工审核
        AfterSalesReviewAutoAuditBO bo = new AfterSalesReviewAutoAuditBO();
        bo.setReviewSn(aftersalesReviews.getReviewSn());
        queue.push(new AfterSalesReviewAutoAuditDisposer(bo));

        return BeanUtil.copyProperties(aftersalesReviews, AfterSalesReviewsVO.class);
    }

    /**
     * 保存行驶证信息
     *
     * @param reviewSn 审核单号
     * @param vehicleInfo 行驶证信息
     * @param dataType 数据类型 [1-审核资料 2-申办资料]
     */
    private void saveVehicleInfo(String reviewSn, CreateAfterSalesReviewDTO.VehicleInfoDTO vehicleInfo, Integer dataType) {
        AftersalesReviewsVehicles vehicle = BeanUtil.copyProperties(vehicleInfo, AftersalesReviewsVehicles.class);
        vehicle.setReviewSn(reviewSn);
        vehicle.setDataType(dataType);

        aftersalesReviewsVehiclesService.create(vehicle);
    }

    /**
     * 取消售后审核单
     *
     * @param dto 取消售后审核单请求参数
     */
    public void cancelAfterSalesReview(CancelAfterSalesReviewDTO dto) {
        // 加锁，防止并发操作
        String lockKey = "afterSalesReview:" + dto.getReviewSn();
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 30)) {
            ToolsHelper.throwException("审核单操作进行中，请稍后再试");
        }

        try {
            // 查询审核单
            AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
            if (aftersalesReviews == null) {
                ToolsHelper.throwException("审核单不存在");
            }

            // 检查审核单状态
            if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.CANCELED.getValue())) {
                return;
            }

            if (Arrays.asList(
                    AftersalesReviewsStatusEnum.APPROVED.getValue(),
                    AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(aftersalesReviews.getReviewStatus())
            ) {
                ToolsHelper.throwException("审核单已有审核结果，无法取消");
            }

            // 更新审核单状态
            aftersalesReviews.setReviewStatus(AftersalesReviewsStatusEnum.CANCELED.getValue()); // 已取消
            aftersalesReviews.setReviewRemark(dto.getCancelReason());
            aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.CANCELED.getValue()); // 已取消
            aftersalesReviews.setUpdatedAt(LocalDateTime.now());

            // 保存审核单
            aftersalesReviewsService.updateById(aftersalesReviews);

            // 记录操作日志
            AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
            reviewsLog.setReviewSn(dto.getReviewSn());
            reviewsLog.setOperateContent("取消售后审核，取消原因：" + dto.getCancelReason());
            reviewsLog.setOperator("system");
            reviewsLog.setType("cancel");
            aftersalesReviewsLogService.create(reviewsLog);

            log.info("取消售后审核单成功，审核单号：{}", dto.getReviewSn());
        } finally {
            // 释放锁
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }

    public IPage<AdminAfterSalesReviewsListVO> getList(AdminAfterSalesReviewsGetListDTO dto) {
        // 分页查询
        IPage<AftersalesReviews> reviewsPage = aftersalesReviewsService.getList(dto);

        // 转换为VO对象
        return reviewsPage.convert(reviews -> {
            AdminAfterSalesReviewsListVO vo = new AdminAfterSalesReviewsListVO();
            BeanUtil.copyProperties(reviews, vo);
            vo.setIssuerName(deliveryConfig.getCnNameByIssuerId(vo.getIssuerId()));
            return vo;
        });
    }

    public AdminAfterSalesReviewsDetailVO getDetail(AdminAfterSalesReviewsDetailDTO dto) {
        // 查询审核单
        AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
        if (aftersalesReviews == null) {
            ToolsHelper.throwException("审核单不存在");
        }

        // 查询审核单行驶证信息
        List<AftersalesReviewsVehicles> vehiclesList = aftersalesReviewsVehiclesService.getByReviewSn(aftersalesReviews.getReviewSn());
        if (ObjectUtils.isEmpty(vehiclesList)) {
            ToolsHelper.throwException("行驶证数据不存在");
        }

        // 获取审核资料和申办资料的数据
        AftersalesReviewsVehicles reviewData = vehiclesList.stream()
                .filter(vehicle -> AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue().equals(vehicle.getDataType()))
                .findFirst()
                .orElse(null);

        AftersalesReviewsVehicles orderData = vehiclesList.stream()
                .filter(vehicle -> AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue().equals(vehicle.getDataType()))
                .findFirst()
                .orElse(null);

        AdminAfterSalesReviewsDetailVO vo = new AdminAfterSalesReviewsDetailVO();
        BeanUtil.copyProperties(aftersalesReviews, vo);
        vo.setReviewVehicleInfo(BeanUtil.copyProperties(reviewData, AdminAfterSalesReviewsDetailVO.VehicleInfoDTO.class));
        vo.setOrderVehicleInfo(BeanUtil.copyProperties(orderData, AdminAfterSalesReviewsDetailVO.VehicleInfoDTO.class));

        return vo;
    }

    public void review(AdminAfReviewsReviewDTO dto) {
        // 加锁，防止并发操作
        String lockKey = "afterSalesReview:" + dto.getReviewSn();
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 30)) {
            ToolsHelper.throwException("审核单操作进行中，请稍后再试");
        }

        try {
            // 获取审核单数据
            AftersalesReviews aftersalesReviews = aftersalesReviewsService.getByReviewSn(dto.getReviewSn());
            if (aftersalesReviews == null) {
                ToolsHelper.throwException("审核单不存在");
            }

            // 检查审核单状态
            if (aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.CANCELED.getValue())) {
                ToolsHelper.throwException("审核单已取消，无法审核");
            }

            if (Arrays.asList(
                    AftersalesReviewsStatusEnum.APPROVED.getValue(),
                    AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(aftersalesReviews.getReviewStatus())
            ) {
                ToolsHelper.throwException("审核单已有审核结果，无法重复审核");
            }

            if (!aftersalesReviews.getReviewStatus().equals(AftersalesReviewsStatusEnum.PENDING.getValue())) {
                ToolsHelper.throwException("只有待审核状态的审核单才能进行审核");
            }

            // 检查领取状态和领取人权限
            String currentOperator = RequestHelper.getAdminOperator();
            if (aftersalesReviews.getReceiveStatus() != null &&
                aftersalesReviews.getReceiveStatus().equals(AftersalesReviewsReceiveStatusEnum.RECEIVED.getValue())) {
                // 已被领取，检查是否是当前操作人领取的
                if (StringUtils.isBlank(aftersalesReviews.getOperator()) ||
                    !aftersalesReviews.getOperator().equals(currentOperator)) {
                    ToolsHelper.throwException("该审核单已被其他人领取，您无权进行审核操作");
                }
            } else {
                // 未被领取，需要先更新领取状态
                aftersalesReviews.setReceiveStatus(AftersalesReviewsReceiveStatusEnum.RECEIVED.getValue());
                aftersalesReviews.setReceiveTime(LocalDateTime.now());
                aftersalesReviews.setOperator(currentOperator);
            }

            // 校验审核状态参数
            if (!Arrays.asList(
                    AftersalesReviewsStatusEnum.APPROVED.getValue(),
                    AftersalesReviewsStatusEnum.REJECTED.getValue()).contains(dto.getReviewStatus())
            ) {
                ToolsHelper.throwException("审核状态参数错误，只能设置为审核通过或审核驳回");
            }

            // 如果是审核驳回，检查拒绝原因
            if (dto.getReviewStatus().equals(AftersalesReviewsStatusEnum.REJECTED.getValue())) {
                if (StringUtils.isBlank(dto.getRejectType())) {
                    ToolsHelper.throwException("审核驳回时必须填写拒绝类型");
                }
                if (ObjectUtils.isEmpty(dto.getRejectReasonId())) {
                    ToolsHelper.throwException("审核驳回时必须选择拒绝原因");
                }
                // 查询具体原因内容
                if (dto.getRejectReasonId() != 0) {
                    Setting reason = settingService.getOneByParams(dto.getRejectReasonId().toString(), SettingKeyEnum.AFTER_SALES_REVIEW.getValue());
                    if (ObjectUtils.isEmpty(reason)) {
                        ToolsHelper.throwException("驳回原因不存在，请刷新页面重新选择");
                    }
                    dto.setRejectReason(reason.getValue());
                }
                // 其他原因
                if (dto.getRejectType().equals(AftersalesReviewsRejectTypeEnum.OTHER.getValue())
                        && dto.getRejectReasonId() == 0
                        && StringUtils.isBlank(dto.getRejectReason())) {
                    ToolsHelper.throwException("审核驳回其他原因时必须填写拒绝原因");
                }
            }

            // 更新审核单状态
            aftersalesReviews.setReviewStatus(dto.getReviewStatus());
            aftersalesReviews.setReviewTime(LocalDateTime.now());
            aftersalesReviews.setOperator(RequestHelper.getAdminOperator());
            aftersalesReviews.setAutoAudit(0); // 人工审核

            // 设置拒绝相关信息
            if (dto.getReviewStatus().equals(AftersalesReviewsStatusEnum.REJECTED.getValue())) {
                aftersalesReviews.setRejectType(dto.getRejectType());
                aftersalesReviews.setRejectReasonId(dto.getRejectReasonId());
                aftersalesReviews.setRejectReason(dto.getRejectReason());
                aftersalesReviews.setReviewRemark(dto.getRejectReason());
            } else {
                // 审核通过时清空拒绝信息
                aftersalesReviews.setRejectType(null);
                aftersalesReviews.setRejectReason(null);
                aftersalesReviews.setReviewRemark("审核通过");
            }

            // 保存审核单
            aftersalesReviews.setUpdatedAt(LocalDateTime.now());
            aftersalesReviewsService.updateById(aftersalesReviews);

            // 记录操作日志
            AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
            reviewsLog.setReviewSn(aftersalesReviews.getReviewSn());
            if (dto.getReviewStatus().equals(AftersalesReviewsStatusEnum.APPROVED.getValue())) {
                reviewsLog.setOperateContent("人工审核通过");
            } else {
                reviewsLog.setOperateContent("人工审核驳回，拒绝原因：" + dto.getRejectReason());
            }
            reviewsLog.setOperator(RequestHelper.getAdminOperator());
            reviewsLog.setType("review");
            aftersalesReviewsLogService.create(reviewsLog);

            log.info("售后审核单审核完成，审核单号：{}，审核结果：{}",
                    aftersalesReviews.getReviewSn(), AftersalesReviewsStatusEnum.map.get(dto.getReviewStatus()));

            // 发送回调通知
            sendNotifyCallback(aftersalesReviews);
        } finally {
            // 释放锁
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }

    public AdminAfterSalesReviewsSelectOptionVO getSelectOption() {
        AdminAfterSalesReviewsSelectOptionVO optionVO = new AdminAfterSalesReviewsSelectOptionVO();
        optionVO.setOrderTypeList(AftersalesReviewsOrderTypeEnum.getSelectOptions());
        optionVO.setReviewStatusList(AftersalesReviewsStatusEnum.getSelectOptions());
        optionVO.setIssuerIdList(deliveryConfig.getIssuerMap().entrySet().stream()
                .map(entry -> Map.of("label", entry.getValue().getCnName(), "value", entry.getKey().toString()))
                .toList());
        optionVO.setAutoAuditList(AftersalesReviewsAutoAuditEnum.getSelectOptions());

        // 获取驳回原因
        List<Setting> afterSalesReviewReasonList = settingService.getAfterSalesReviewReasonList();
        Map<String, Map<String, String>> reasonMap = afterSalesReviewReasonList.stream()
                .collect(Collectors.groupingBy(Setting::getCategory,
                        Collectors.toMap(Setting::getParams, Setting::getValue, (v1, v2) -> v1)));

        // 驳回原因分类
        List<MultiSelectOptionVO> reasonList = SettingCategoryEnum.afterSalesList.stream().map(category -> {
            MultiSelectOptionVO multiSelectOptionVO = new MultiSelectOptionVO();
            multiSelectOptionVO.setLabel(SettingCategoryEnum.map.get(category));
            multiSelectOptionVO.setValue(category);

            // 将每个原因转换为 MultiSelectOptionVO 对象
            Map<String, String> categoryReasons = reasonMap.getOrDefault(category, Collections.emptyMap());
            List<MultiSelectOptionVO> childrenList = categoryReasons.entrySet().stream()
                    .map(entry -> {
                        MultiSelectOptionVO childOption = new MultiSelectOptionVO();
                        childOption.setLabel(entry.getValue());  // 设置显示文本为value字段
                        childOption.setValue(entry.getKey());  // 设置值为id字段
                        return childOption;
                    })
                    .toList();

            multiSelectOptionVO.setChildren(childrenList);
            return multiSelectOptionVO;
        }).toList();
        optionVO.setRejectReasonList(reasonList);
        return optionVO;
    }

    /**
     * 批量领取售后审核单
     *
     * @param dto 批量领取请求参数
     * @return 领取到的售后审核单号列表
     */
    public AdminAfterSalesReviewsBatchReceiveVO batchReceive(AdminAfterSalesReviewsBatchReceiveDTO dto) {
        String operator = RequestHelper.getAdminOperator();

        // 查询待领取的售后审核单
        List<AftersalesReviews> pendingRecords = aftersalesReviewsService.getPendingReceiveRecords(dto.getCount());

        if (CollectionUtils.isEmpty(pendingRecords)) {
            ToolsHelper.throwException("暂无可领取的售后审核单");
        }

        // 提取审核单号列表
        List<String> reviewSnList = pendingRecords.stream()
                .map(AftersalesReviews::getReviewSn)
                .collect(Collectors.toList());

        // 批量更新领取状态
        int updateCount = aftersalesReviewsService.batchUpdateReceiveStatus(reviewSnList, operator);

        if (updateCount == 0) {
            log.warn("批量领取售后审核单失败，可能已被其他人领取，操作人：{}", operator);
            ToolsHelper.throwException("领取失败，审核单可能已被其他人领取");
        }

        // 记录操作日志
        for (String reviewSn : reviewSnList) {
            AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
            reviewsLog.setReviewSn(reviewSn);
            reviewsLog.setOperateContent("批量领取售后审核单");
            reviewsLog.setOperator(operator);
            reviewsLog.setType("batch_receive");
            aftersalesReviewsLogService.create(reviewsLog);
        }

        log.info("批量领取售后审核单成功，操作人：{}，领取数量：{}", operator, updateCount);

        // 返回审核单号列表
        return new AdminAfterSalesReviewsBatchReceiveVO(reviewSnList);
    }

    /**
     * 发送回调通知
     *
     * @param aftersalesReviews 售后审核单数据
     */
    public void sendNotifyCallback(AftersalesReviews aftersalesReviews) {
        // 检查是否有回调地址
        if (StringUtils.isBlank(aftersalesReviews.getNotifyUrl())) {
            log.debug("审核单号: {} 没有配置回调地址，跳过回调通知", aftersalesReviews.getReviewSn());
            return;
        }

        // 没有审核结果 不能通知
        if (!Arrays.asList(AftersalesReviewsStatusEnum.APPROVED.getValue(), AftersalesReviewsStatusEnum.REJECTED.getValue())
                .contains(aftersalesReviews.getReviewStatus())) {
            log.error("审核单号：{} 没有审核结果，不能通知", aftersalesReviews.getReviewSn());
            return;
        }

        try {
            // 构建回调数据
            AfterSalesReviewsNotifyDTO notifyDTO = BeanUtil.copyProperties(aftersalesReviews, AfterSalesReviewsNotifyDTO.class);

            // 构建回调地址
            URI uri = URI.create(aftersalesReviews.getNotifyUrl());

            // 发送回调通知
            JsonResult<Object> result = afterSalesReviewsNotifyFeign.reviewStatusNotify(uri, notifyDTO);

            if (Boolean.TRUE.equals(result.isSuccess())) {
                // 通知成功，更新通知状态
                aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.SUCCESS.getValue());
                aftersalesReviews.setUpdatedAt(LocalDateTime.now());
                aftersalesReviewsService.updateById(aftersalesReviews);

                log.info("售后审核单回调通知成功，审核单号: {}, 回调地址: {}",
                        aftersalesReviews.getReviewSn(), aftersalesReviews.getNotifyUrl());
            } else {
                // 通知失败，更新通知状态
                aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.FAILED.getValue());
                aftersalesReviews.setUpdatedAt(LocalDateTime.now());
                aftersalesReviewsService.updateById(aftersalesReviews);

                log.warn("售后审核单回调通知失败，审核单号: {}, 回调地址: {}, 错误信息: {}",
                        aftersalesReviews.getReviewSn(), aftersalesReviews.getNotifyUrl(), result.getMsg());
            }
        } catch (Exception e) {
            // 通知异常，更新通知状态为失败
            aftersalesReviews.setNotifyStatus(AftersalesReviewsNotifyStatusEnum.FAILED.getValue());
            aftersalesReviews.setUpdatedAt(LocalDateTime.now());
            aftersalesReviewsService.updateById(aftersalesReviews);

            log.error("售后审核单回调通知异常，审核单号: {}, 回调地址: {}",
                    aftersalesReviews.getReviewSn(), aftersalesReviews.getNotifyUrl(), e);
        }
    }
}
