package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsNotifyStatusEnum;
import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.delivery.application.common.enums.aftersalesreviews.AftersalesReviewsReceiveStatusEnum;
import com.ets.delivery.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsGetListDTO;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.delivery.application.infra.mapper.AftersalesReviewsMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@DS("db-issuer-admin-proxy")
public class AftersalesReviewsService extends BaseService<AftersalesReviewsMapper, AftersalesReviews> {

    public IPage<AftersalesReviews> getList(AdminAfterSalesReviewsGetListDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<AftersalesReviews> queryWrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(StringUtils.isNotBlank(dto.getOrderType()), AftersalesReviews::getOrderType, dto.getOrderType())
                .eq(dto.getIssuerId() != null, AftersalesReviews::getIssuerId, dto.getIssuerId())
                .eq(StringUtils.isNotBlank(dto.getPlateNo()), AftersalesReviews::getPlateNo, dto.getPlateNo())
                .eq(dto.getUid() != null, AftersalesReviews::getUid, dto.getUid())
                .eq(StringUtils.isNotBlank(dto.getOrderSn()), AftersalesReviews::getOrderSn, dto.getOrderSn())
                .eq(StringUtils.isNotBlank(dto.getReviewSn()), AftersalesReviews::getReviewSn, dto.getReviewSn())
                .eq(dto.getReviewStatus() != null, AftersalesReviews::getReviewStatus, dto.getReviewStatus())
                .eq(dto.getRejectReasonId() != null, AftersalesReviews::getRejectReasonId, dto.getRejectReasonId())
                .eq(dto.getAutoAudit() != null, AftersalesReviews::getAutoAudit, dto.getAutoAudit())
                .eq(StringUtils.isNotBlank(dto.getOperator()), AftersalesReviews::getOperator, dto.getOperator())
                .orderByDesc(AftersalesReviews::getCreatedAt);

        if (ObjectUtils.isNotEmpty(dto.getApplyTimeStart())) {
            queryWrapper.ge(AftersalesReviews::getApplyTime, dto.getApplyTimeStart().atStartOfDay());
        }
        if (ObjectUtils.isNotEmpty(dto.getApplyTimeEnd())) {
            queryWrapper.le(AftersalesReviews::getApplyTime, dto.getApplyTimeEnd().atTime(LocalTime.MAX));
        }
        if (ObjectUtils.isNotEmpty(dto.getReviewTimeStart())) {
            queryWrapper.ge(AftersalesReviews::getReviewTime, dto.getReviewTimeStart().atStartOfDay());
        }
        if (ObjectUtils.isNotEmpty(dto.getReviewTimeEnd())) {
            queryWrapper.le(AftersalesReviews::getReviewTime, dto.getReviewTimeEnd().atTime(LocalTime.MAX));
        }

        return this.page(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
    }

    public AftersalesReviews getByOrderSn(String orderSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getOrderSn, orderSn)
                .orderByDesc(AftersalesReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    public AftersalesReviews getByReviewSn(String reviewSn) {
        Wrapper<AftersalesReviews> wrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                .eq(AftersalesReviews::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 查询通知失败的审核单记录
     *
     * @param hours 多少小时内的记录
     * @param limit 限制数量
     * @return 通知失败的审核单列表
     */
    public List<AftersalesReviews> getNotifyFailedRecords(int hours, int limit) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);

        LambdaQueryWrapper<AftersalesReviews> queryWrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                // 通知状态为失败
                .eq(AftersalesReviews::getNotifyStatus, AftersalesReviewsNotifyStatusEnum.FAILED.getValue())
                // 审核状态为已审核（通过或驳回）
                .in(AftersalesReviews::getReviewStatus, Arrays.asList(
                        AftersalesReviewsStatusEnum.APPROVED.getValue(),
                        AftersalesReviewsStatusEnum.REJECTED.getValue()))
                // 有回调地址
                .isNotNull(AftersalesReviews::getNotifyUrl)
                .ne(AftersalesReviews::getNotifyUrl, "")
                // 指定时间范围内的记录
                .ge(AftersalesReviews::getUpdatedAt, startTime)
                // 按更新时间排序
                .orderByAsc(AftersalesReviews::getUpdatedAt)
                // 限制数量
                .last("limit " + limit);

        return this.list(queryWrapper);
    }

    /**
     * 查询待领取的售后审核单
     *
     * @param count 查询数量
     * @return 待领取的售后审核单列表
     */
    public List<AftersalesReviews> getPendingReceiveRecords(Integer count) {
        LambdaQueryWrapper<AftersalesReviews> queryWrapper = Wrappers.<AftersalesReviews>lambdaQuery()
                // 审核状态为待审核
                .eq(AftersalesReviews::getReviewStatus, AftersalesReviewsStatusEnum.PENDING.getValue())
                // 领取状态为未领取或null
                .and(wrapper -> wrapper.isNull(AftersalesReviews::getReceiveStatus)
                        .or().eq(AftersalesReviews::getReceiveStatus, AftersalesReviewsReceiveStatusEnum.NOT_RECEIVED.getValue()))
                // 按申请时间升序排序，优先处理早期申请的
                .orderByAsc(AftersalesReviews::getApplyTime);

        // 限制查询数量
        if (count != null && count > 0) {
            queryWrapper.last("limit " + count);
        }

        return this.list(queryWrapper);
    }

    /**
     * 批量更新领取状态
     *
     * @param reviewSnList 审核单号列表
     * @param operator 操作人
     * @return 更新成功的数量
     */
    public int batchUpdateReceiveStatus(List<String> reviewSnList, String operator) {
        if (CollectionUtils.isEmpty(reviewSnList)) {
            return 0;
        }

        LambdaUpdateWrapper<AftersalesReviews> updateWrapper = Wrappers.<AftersalesReviews>lambdaUpdate()
                .in(AftersalesReviews::getReviewSn, reviewSnList)
                // 只更新未领取的记录
                .and(wrapper -> wrapper.isNull(AftersalesReviews::getReceiveStatus)
                        .or().eq(AftersalesReviews::getReceiveStatus, AftersalesReviewsReceiveStatusEnum.NOT_RECEIVED.getValue()))
                .set(AftersalesReviews::getReceiveStatus, AftersalesReviewsReceiveStatusEnum.RECEIVED.getValue())
                .set(AftersalesReviews::getReceiveTime, LocalDateTime.now())
                .set(AftersalesReviews::getOperator, operator)
                .set(AftersalesReviews::getUpdatedAt, LocalDateTime.now());

        return this.update(updateWrapper);
    }
}
