package com.ets.delivery.application.app.job;

import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviewsLog;
import com.ets.delivery.application.infra.service.AftersalesReviewsLogService;
import com.ets.delivery.application.infra.service.AftersalesReviewsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后审核超时处理定时任务
 */
@Slf4j
@Component
public class AfterSalesReviewsTimeoutJob {

    @Autowired
    private AftersalesReviewsService aftersalesReviewsService;

    @Autowired
    private AftersalesReviewsLogService aftersalesReviewsLogService;

    /**
     * 处理已领取超时未审核的售后审核单
     *
     * @param params 参数格式：hours,limit 例如：1,100 表示查询1小时内最多100条超时记录
     * @return 执行结果
     */
    @XxlJob("resetTimeoutAfterSalesReviewsHandler")
    public ReturnT<String> resetTimeoutAfterSalesReviewsHandler(String params) {
        try {
            // 解析参数
            int hours = 1; // 默认1小时
            int limit = 100; // 默认100条

            if (StringUtils.isNotBlank(params)) {
                String[] paramArray = params.split(",");
                if (paramArray.length >= 1) {
                    hours = Integer.parseInt(paramArray[0].trim());
                }
                if (paramArray.length >= 2) {
                    limit = Integer.parseInt(paramArray[1].trim());
                }
            }

            // 限制参数范围
            hours = Math.max(1, Math.min(hours, 24)); // 1-24小时
            limit = Math.max(1, Math.min(limit, 1000)); // 1-1000条

            XxlJobLogger.log("开始处理超时未审核的售后审核单，查询参数：{}小时内，最多{}条记录", hours, limit);

            // 查询超时记录
            List<AftersalesReviews> timeoutRecords = aftersalesReviewsService.getTimeoutReceivedRecords(hours, limit);

            if (CollectionUtils.isEmpty(timeoutRecords)) {
                XxlJobLogger.log("没有找到需要处理的超时记录");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("找到{}条超时记录，开始重置为未领取状态", timeoutRecords.size());

            // 提取审核单号列表
            List<String> reviewSnList = timeoutRecords.stream()
                    .map(AftersalesReviews::getReviewSn)
                    .collect(Collectors.toList());

            // 批量重置领取状态
            int resetCount = aftersalesReviewsService.resetReceiveStatus(reviewSnList);

            if (resetCount > 0) {
                // 记录操作日志
                for (String reviewSn : reviewSnList) {
                    try {
                        AftersalesReviewsLog reviewsLog = new AftersalesReviewsLog();
                        reviewsLog.setReviewSn(reviewSn);
                        reviewsLog.setOperateContent("超时未审核，系统自动重置为未领取状态");
                        reviewsLog.setOperator("system");
                        reviewsLog.setType("timeout_reset");
                        aftersalesReviewsLogService.create(reviewsLog);
                    } catch (Exception e) {
                        log.warn("记录超时重置日志失败，审核单号：{}", reviewSn, e);
                    }
                }
            }

            String resultMsg = String.format("处理完成，重置了%d条超时记录", resetCount);
            XxlJobLogger.log(resultMsg);
            log.info("售后审核超时处理任务完成，{}", resultMsg);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            String errorMsg = "售后审核超时处理任务执行异常：" + e.getMessage();
            log.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMsg);
        }
    }
}
